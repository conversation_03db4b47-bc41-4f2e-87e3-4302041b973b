{"hash": "16482f22", "configHash": "6f1f7336", "lockfileHash": "0c7e1125", "browserHash": "e3a232a4", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "7345e612", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "97aa1562", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "1d13b891", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "5fded28e", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "2cadb961", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "a6b60f72", "needsInterop": false}, "@radix-ui/react-navigation-menu": {"src": "../../@radix-ui/react-navigation-menu/dist/index.mjs", "file": "@radix-ui_react-navigation-menu.js", "fileHash": "8af311be", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "ed3890fe", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "981a91ab", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "a69f94c0", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "5452e781", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "e15fe651", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "758bb8ea", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "7b475496", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "2180fbfa", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "f05224ef", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "92eca88e", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "6158cdf6", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "79c3cc3a", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "8c5c65a0", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "908bcf7c", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0a04801b", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "692ec47d", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "931954f2", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ab3105c2", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "a1e5c0af", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "a657c740", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "e0024c7b", "needsInterop": false}, "zod": {"src": "../../zod/lib/index.mjs", "file": "zod.js", "fileHash": "04bf0806", "needsInterop": false}}, "chunks": {"browser-4V3VJP7U": {"file": "browser-4V3VJP7U.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-4DG36E2S": {"file": "chunk-4DG36E2S.js"}, "chunk-EV5STJTR": {"file": "chunk-EV5STJTR.js"}, "chunk-POFWI3HG": {"file": "chunk-POFWI3HG.js"}, "chunk-T7A5WWLG": {"file": "chunk-T7A5WWLG.js"}, "chunk-AX2BD4GE": {"file": "chunk-AX2BD4GE.js"}, "chunk-MEIIQWFS": {"file": "chunk-MEIIQWFS.js"}, "chunk-4XLYOFXT": {"file": "chunk-4XLYOFXT.js"}, "chunk-ZV5VJJSX": {"file": "chunk-ZV5VJJSX.js"}, "chunk-V5JOJVJU": {"file": "chunk-V5JOJVJU.js"}, "chunk-SXBBHXIZ": {"file": "chunk-SXBBHXIZ.js"}, "chunk-DXEVUYT3": {"file": "chunk-DXEVUYT3.js"}, "chunk-HKKIFKAO": {"file": "chunk-HKKIFKAO.js"}, "chunk-WG5EJHRK": {"file": "chunk-WG5EJHRK.js"}, "chunk-6ZMM2PAV": {"file": "chunk-6ZMM2PAV.js"}, "chunk-DV3DWD4L": {"file": "chunk-DV3DWD4L.js"}, "chunk-HVSDMX22": {"file": "chunk-HVSDMX22.js"}, "chunk-OD433RWB": {"file": "chunk-OD433RWB.js"}, "chunk-2FYCGHUN": {"file": "chunk-2FYCGHUN.js"}, "chunk-VNAZQ33N": {"file": "chunk-VNAZQ33N.js"}, "chunk-FJ2A54M7": {"file": "chunk-FJ2A54M7.js"}, "chunk-L3SKXKGI": {"file": "chunk-L3SKXKGI.js"}, "chunk-WKPQ4ZTV": {"file": "chunk-WKPQ4ZTV.js"}, "chunk-BG45W2ER": {"file": "chunk-BG45W2ER.js"}, "chunk-HXA6O6EE": {"file": "chunk-HXA6O6EE.js"}}}